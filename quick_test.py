#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 用少量样本测试API效果和速度
"""

import time
from qwen_enhanced_dataset_builder import EmotionGuidanceDatasetBuilder

def quick_test():
    """快速测试API调用效果"""
    print("🚀 快速测试模式 - 处理5个样本")
    
    # 使用您的API密钥
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    # 创建构建器（启用快速模式）
    builder = EmotionGuidanceDatasetBuilder(API_KEY, fast_mode=True)
    
    start_time = time.time()
    
    # 只处理5个样本进行测试
    builder.build_dataset(
        input_file="child_chat_data.json",
        output_file="quick_test_result.json",
        target_ages=["12-18m"],  # 只测试一个年龄段
        max_samples=5  # 只处理5个样本
    )
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️  测试完成！")
    print(f"总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    print(f"平均每个样本: {total_time/5:.1f}秒")
    
    # 估算完整处理时间
    full_samples = 200
    estimated_full_time = (total_time / 5) * full_samples
    print(f"\n📊 完整处理{full_samples}个样本预计耗时: {estimated_full_time/60:.1f}分钟 ({estimated_full_time/3600:.1f}小时)")

def speed_comparison():
    """速度对比测试"""
    print("\n🏃‍♂️ 速度对比测试")
    
    models = ["qwen-turbo", "qwen-plus", "qwen-max"]
    
    for model in models:
        print(f"\n测试模型: {model}")
        # 这里可以测试不同模型的响应速度
        # 实际实现时可以修改QwenAPIClient来支持模型切换

if __name__ == "__main__":
    quick_test()
    
    print("\n💡 优化建议:")
    print("1. 如果速度可以接受，继续使用qwen-max获得最佳质量")
    print("2. 如果太慢，可以切换到qwen-turbo或qwen-plus")
    print("3. 可以先处理少量样本验证效果，再批量处理")
    print("4. 考虑分批处理，避免长时间运行")

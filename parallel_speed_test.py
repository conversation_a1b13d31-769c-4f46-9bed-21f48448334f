#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理速度测试 - 对比串行vs并行的处理速度
"""

import time
import json
from concurrent.futures import ThreadPoolExecutor
from qwen_enhanced_dataset_builder import EmotionGuidanceDatasetBuilder

def serial_processing_test(api_key: str, samples_count: int = 5):
    """串行处理测试"""
    print(f"🐌 串行处理测试 ({samples_count}个样本)")
    
    # 加载数据
    with open('child_chat_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    selected_samples = data[:samples_count]
    
    builder = EmotionGuidanceDatasetBuilder(api_key, fast_mode=True, model="qwen-turbo")
    
    start_time = time.time()
    results = []
    
    for i, sample in enumerate(selected_samples):
        print(f"  处理样本 {i+1}/{samples_count}")
        result = builder.process_dialogue_sample(sample, "12-18m")
        if result:
            results.append(result)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"串行处理完成: {total_time:.1f}秒, 平均 {total_time/samples_count:.1f}秒/样本")
    return total_time, len(results)

def parallel_processing_test(api_key: str, samples_count: int = 5, workers: int = 3):
    """并行处理测试"""
    print(f"🚀 并行处理测试 ({samples_count}个样本, {workers}个线程)")
    
    # 加载数据
    with open('child_chat_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    selected_samples = data[:samples_count]
    
    def process_single(sample_with_index):
        sample, index = sample_with_index
        builder = EmotionGuidanceDatasetBuilder(api_key, fast_mode=True, model="qwen-turbo")
        print(f"  🔄 线程处理样本 {index+1}")
        result = builder.process_dialogue_sample(sample, "12-18m")
        if result:
            print(f"  ✅ 线程完成样本 {index+1}")
        return result
    
    start_time = time.time()
    results = []
    
    # 准备任务
    tasks = [(sample, i) for i, sample in enumerate(selected_samples)]
    
    # 并行执行
    with ThreadPoolExecutor(max_workers=workers) as executor:
        futures = [executor.submit(process_single, task) for task in tasks]
        
        for future in futures:
            result = future.result()
            if result:
                results.append(result)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"并行处理完成: {total_time:.1f}秒, 平均 {total_time/samples_count:.1f}秒/样本")
    return total_time, len(results)

def speed_comparison():
    """速度对比测试"""
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    print("⚡ 并行处理速度对比测试")
    print("=" * 50)
    
    # 测试参数
    test_samples = 6  # 测试样本数
    
    # 串行处理测试
    serial_time, serial_results = serial_processing_test(API_KEY, test_samples)
    
    print("\n" + "=" * 50)
    
    # 并行处理测试 (不同线程数)
    parallel_configs = [
        {"workers": 2, "name": "2线程"},
        {"workers": 3, "name": "3线程"},
        {"workers": 4, "name": "4线程"}
    ]
    
    best_config = None
    best_speedup = 0
    
    for config in parallel_configs:
        print(f"\n{'-' * 30}")
        parallel_time, parallel_results = parallel_processing_test(
            API_KEY, test_samples, config["workers"]
        )
        
        if parallel_time > 0:
            speedup = serial_time / parallel_time
            efficiency = speedup / config["workers"] * 100
            
            print(f"\n📊 {config['name']} 性能分析:")
            print(f"  加速比: {speedup:.2f}x")
            print(f"  效率: {efficiency:.1f}%")
            print(f"  时间节省: {(serial_time - parallel_time)/60:.1f}分钟")
            
            if speedup > best_speedup:
                best_speedup = speedup
                best_config = config
    
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    print(f"串行处理: {serial_time:.1f}秒 ({serial_time/test_samples:.1f}秒/样本)")
    
    if best_config:
        best_time = serial_time / best_speedup
        print(f"最佳并行: {best_config['name']} - {best_time:.1f}秒 ({best_speedup:.2f}x加速)")
        
        # 预估大规模处理时间
        samples_200 = 200
        serial_estimate = (serial_time / test_samples) * samples_200
        parallel_estimate = (best_time / test_samples) * samples_200
        
        print(f"\n📈 200样本处理时间预估:")
        print(f"串行处理: {serial_estimate/3600:.1f}小时")
        print(f"并行处理: {parallel_estimate/3600:.1f}小时")
        print(f"时间节省: {(serial_estimate - parallel_estimate)/3600:.1f}小时")
    
    print(f"\n💡 建议:")
    if best_config:
        print(f"- 使用 {best_config['name']} 进行大规模处理")
        print(f"- 预计加速比: {best_speedup:.2f}x")
    print("- 可以进一步调整线程数优化性能")
    print("- 注意API调用频率限制")

def quick_parallel_demo():
    """快速并行演示"""
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    print("🚀 快速并行处理演示")
    
    from parallel_processor import ParallelProcessor
    
    processor = ParallelProcessor(
        API_KEY,
        max_workers=3,
        model="qwen-turbo"
    )
    
    start_time = time.time()
    
    results = processor.parallel_process_threading(
        input_file="child_chat_data.json",
        output_file="quick_parallel_demo.json",
        target_ages=["12-18m"],
        max_samples=10
    )
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 快速演示结果:")
    print(f"处理时间: {total_time/60:.1f}分钟")
    print(f"成功样本: {len(results)}")
    print(f"平均速度: {total_time/len(results):.1f}秒/样本")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 速度对比测试 (串行 vs 并行)")
    print("2. 快速并行演示")
    
    choice = input("请选择 (1/2): ").strip() or "1"
    
    if choice == "1":
        speed_comparison()
    elif choice == "2":
        quick_parallel_demo()
    else:
        print("无效选择")

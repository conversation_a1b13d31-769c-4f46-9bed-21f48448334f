#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿社会情绪发展指导数据集生成器
基于现有PEC数据集，生成适合0-3岁婴幼儿的社会情绪发展指导数据
"""

import json
import random
from typing import Dict, List, Any
from datetime import datetime

class InfantEmotionDatasetGenerator:
    def __init__(self):
        self.age_ranges = {
            "0-1m": {"name": "新生儿期", "months": [0]},
            "1-6m": {"name": "婴儿早期", "months": [1, 2, 3, 4, 5, 6]},
            "6-12m": {"name": "婴儿晚期", "months": [6, 7, 8, 9, 10, 11, 12]},
            "12-18m": {"name": "幼儿早期", "months": [12, 13, 14, 15, 16, 17, 18]},
            "18-24m": {"name": "幼儿中期", "months": [18, 19, 20, 21, 22, 23, 24]},
            "24-36m": {"name": "幼儿晚期", "months": [24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]}
        }
        
        self.development_domains = {
            "emotion": "情绪发展",
            "social": "社会交往", 
            "cognitive": "认知发展",
            "motor": "运动发展",
            "language": "语言发展"
        }
        
        self.scenario_types = {
            "daily_care": "日常护理",
            "milestone": "发育里程碑", 
            "problem_behavior": "问题行为",
            "assessment": "发育评估"
        }
        
        # 婴幼儿行为表现词汇库
        self.infant_behaviors = {
            "0-1m": ["哭闹", "安静", "睡眠", "吃奶", "注视", "微弱笑容"],
            "1-6m": ["微笑", "咿呀学语", "抓握", "翻身", "俯卧抬头", "追视"],
            "6-12m": ["坐立", "爬行", "拍手", "挥手", "叫妈妈", "认生"],
            "12-18m": ["走路", "指认", "模仿", "分离焦虑", "探索", "简单词汇"],
            "18-24m": ["跑跳", "两词句", "自我意识", "情绪爆发", "模仿游戏", "社交微笑"],
            "24-36m": ["完整句子", "角色游戏", "情绪调节", "同伴互动", "自主性", "规则理解"]
        }
        
        # 情绪状态映射
        self.emotion_mapping = {
            "happy": "愉快/兴奋",
            "sad": "难过/不安", 
            "angry": "烦躁/愤怒",
            "fear": "恐惧/紧张",
            "surprise": "好奇/惊讶",
            "disgust": "不适/拒绝"
        }

    def load_original_data(self, file_path: str) -> List[Dict]:
        """加载原始PEC数据集"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def adapt_scenario_to_infant(self, original_scenario: Dict, target_age: str) -> Dict:
        """将原始场景适配为婴幼儿场景"""
        topic = original_scenario.get('topic', '')
        
        # 场景映射规则
        infant_scenarios = {
            "吃饭": self._create_feeding_scenario,
            "睡觉": self._create_sleep_scenario,
            "游戏": self._create_play_scenario,
            "哭闹": self._create_crying_scenario,
            "洗澡": self._create_bathing_scenario,
            "外出": self._create_outing_scenario,
            "社交": self._create_social_scenario,
            "学习": self._create_learning_scenario
        }
        
        # 根据原始话题选择适配函数
        for key, func in infant_scenarios.items():
            if key in topic or self._topic_similarity(topic, key):
                return func(original_scenario, target_age)
        
        # 默认适配
        return self._create_general_scenario(original_scenario, target_age)

    def _create_feeding_scenario(self, original: Dict, age: str) -> Dict:
        """创建喂养场景"""
        behaviors = random.sample(self.infant_behaviors[age], 2)
        
        if age in ["0-1m", "1-6m"]:
            context = "宝宝在喂奶时表现出不同的反应"
            guidance = [
                {"speaker": "system", "content": f"观察到宝宝{behaviors[0]}，这可能表示...", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "我注意到宝宝的表情和动作变化", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "这是正常的喂养反应，建议...", "guidance_type": "response"}
            ]
        else:
            context = "宝宝在自主进食时的表现"
            guidance = [
                {"speaker": "system", "content": f"宝宝{behaviors[0]}，显示出自主进食的意愿", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "我应该如何支持宝宝的自主进食？", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "可以提供适合的手指食物，鼓励自主探索", "guidance_type": "strategy"}
            ]
        
        return {
            "scenario_id": f"feeding_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "emotion",
            "scenario_type": "daily_care",
            "context": {
                "setting": "home",
                "participants": ["caregiver", "infant"],
                "background": context
            },
            "infant_behavior": {
                "observable_signs": behaviors,
                "emotional_state": random.choice(["happy", "calm", "frustrated"]),
                "developmental_indicators": ["feeding_skills", "self_regulation"]
            },
            "guidance_dialogue": guidance,
            "learning_objectives": ["观察婴儿喂养信号", "支持自主进食发展"],
            "follow_up_suggestions": ["继续观察进食模式", "调整食物质地"]
        }

    def _create_sleep_scenario(self, original: Dict, age: str) -> Dict:
        """创建睡眠场景"""
        behaviors = random.sample(self.infant_behaviors[age], 2)
        
        return {
            "scenario_id": f"sleep_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "emotion",
            "scenario_type": "daily_care",
            "context": {
                "setting": "home",
                "participants": ["caregiver", "infant"],
                "background": "宝宝在睡眠时间的表现和需求"
            },
            "infant_behavior": {
                "observable_signs": behaviors + ["揉眼睛", "打哈欠"],
                "emotional_state": random.choice(["tired", "restless", "calm"]),
                "developmental_indicators": ["sleep_patterns", "self_soothing"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": f"宝宝出现{behaviors[0]}的行为", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "这是要睡觉的信号吗？", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "是的，建议创造安静的睡眠环境", "guidance_type": "strategy"}
            ],
            "learning_objectives": ["识别睡眠信号", "建立睡眠规律"],
            "follow_up_suggestions": ["记录睡眠模式", "调整睡眠环境"]
        }

    def _create_play_scenario(self, original: Dict, age: str) -> Dict:
        """创建游戏场景"""
        behaviors = random.sample(self.infant_behaviors[age], 3)
        
        return {
            "scenario_id": f"play_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "cognitive",
            "scenario_type": "milestone",
            "context": {
                "setting": "home",
                "participants": ["caregiver", "infant"],
                "background": "宝宝在游戏中的探索和学习行为"
            },
            "infant_behavior": {
                "observable_signs": behaviors,
                "emotional_state": "happy",
                "developmental_indicators": ["play_skills", "exploration", "attention"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": f"宝宝{behaviors[0]}，展现出强烈的探索欲", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "我应该如何回应宝宝的游戏行为？", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "跟随宝宝的兴趣，提供适龄的游戏材料", "guidance_type": "strategy"}
            ],
            "learning_objectives": ["支持探索行为", "促进认知发展"],
            "follow_up_suggestions": ["观察游戏偏好", "丰富游戏材料"]
        }

    def _create_crying_scenario(self, original: Dict, age: str) -> Dict:
        """创建哭闹安抚场景"""
        return {
            "scenario_id": f"crying_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "emotion",
            "scenario_type": "problem_behavior",
            "context": {
                "setting": "home",
                "participants": ["caregiver", "infant"],
                "background": "宝宝出现哭闹，需要识别原因并给予适当安抚"
            },
            "infant_behavior": {
                "observable_signs": ["哭闹", "身体紧张", "拒绝安抚"],
                "emotional_state": "distressed",
                "developmental_indicators": ["communication_needs", "self_regulation"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": "宝宝持续哭闹，需要系统性评估", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "我已经检查了基本需求，但宝宝还在哭", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "尝试不同的安抚技巧：抱抱、轻摇、白噪音", "guidance_type": "strategy"}
            ],
            "learning_objectives": ["识别哭闹原因", "掌握安抚技巧"],
            "follow_up_suggestions": ["记录哭闹模式", "寻求专业建议"]
        }

    def _create_bathing_scenario(self, original: Dict, age: str) -> Dict:
        """创建洗澡场景"""
        behaviors = random.sample(self.infant_behaviors[age], 2)

        return {
            "scenario_id": f"bathing_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "emotion",
            "scenario_type": "daily_care",
            "context": {
                "setting": "home",
                "participants": ["caregiver", "infant"],
                "background": "宝宝在洗澡时的反应和适应"
            },
            "infant_behavior": {
                "observable_signs": behaviors + ["踢水", "紧张"],
                "emotional_state": random.choice(["happy", "anxious", "calm"]),
                "developmental_indicators": ["sensory_processing", "body_awareness"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": f"宝宝在水中{behaviors[0]}", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "宝宝似乎对水温有反应", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "调整水温，观察宝宝的舒适度", "guidance_type": "strategy"}
            ],
            "learning_objectives": ["观察感官反应", "创造舒适体验"],
            "follow_up_suggestions": ["记录洗澡偏好", "逐步增加水中游戏"]
        }

    def _create_outing_scenario(self, original: Dict, age: str) -> Dict:
        """创建外出场景"""
        behaviors = random.sample(self.infant_behaviors[age], 2)

        return {
            "scenario_id": f"outing_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "social",
            "scenario_type": "daily_care",
            "context": {
                "setting": "outdoor",
                "participants": ["caregiver", "infant", "strangers"],
                "background": "宝宝在外出时对新环境的反应"
            },
            "infant_behavior": {
                "observable_signs": behaviors + ["东张西望", "认生"],
                "emotional_state": random.choice(["curious", "anxious", "excited"]),
                "developmental_indicators": ["environmental_adaptation", "social_awareness"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": f"宝宝{behaviors[0]}，对新环境表现出反应", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "宝宝对陌生环境有些紧张", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "给宝宝时间适应，提供安全感", "guidance_type": "strategy"}
            ],
            "learning_objectives": ["支持环境适应", "建立安全感"],
            "follow_up_suggestions": ["逐步增加外出时间", "观察适应能力"]
        }

    def _create_social_scenario(self, original: Dict, age: str) -> Dict:
        """创建社交场景"""
        behaviors = random.sample(self.infant_behaviors[age], 2)

        return {
            "scenario_id": f"social_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "social",
            "scenario_type": "milestone",
            "context": {
                "setting": "home",
                "participants": ["caregiver", "infant", "visitor"],
                "background": "宝宝与他人的社交互动"
            },
            "infant_behavior": {
                "observable_signs": behaviors + ["微笑", "回避"],
                "emotional_state": random.choice(["friendly", "shy", "curious"]),
                "developmental_indicators": ["social_engagement", "stranger_reaction"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": f"宝宝看到陌生人时{behaviors[0]}", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "这是正常的社交反应吗？", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "是的，这显示了社交意识的发展", "guidance_type": "response"}
            ],
            "learning_objectives": ["理解社交发展", "支持社交技能"],
            "follow_up_suggestions": ["创造安全的社交机会", "观察社交偏好"]
        }

    def _create_learning_scenario(self, original: Dict, age: str) -> Dict:
        """创建学习场景"""
        behaviors = random.sample(self.infant_behaviors[age], 2)

        return {
            "scenario_id": f"learning_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": "cognitive",
            "scenario_type": "milestone",
            "context": {
                "setting": "home",
                "participants": ["caregiver", "infant"],
                "background": "宝宝在学习和探索中的表现"
            },
            "infant_behavior": {
                "observable_signs": behaviors + ["专注", "模仿"],
                "emotional_state": "curious",
                "developmental_indicators": ["learning_capacity", "attention_span"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": f"宝宝{behaviors[0]}，展现出学习兴趣", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "如何更好地支持宝宝的学习？", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "提供适龄的刺激和互动机会", "guidance_type": "strategy"}
            ],
            "learning_objectives": ["促进认知发展", "支持学习兴趣"],
            "follow_up_suggestions": ["观察学习模式", "调整刺激强度"]
        }

    def _create_general_scenario(self, original: Dict, age: str) -> Dict:
        """创建通用场景"""
        behaviors = random.sample(self.infant_behaviors[age], 2)

        return {
            "scenario_id": f"general_{age}_{random.randint(1000, 9999)}",
            "age_range": age,
            "development_domain": random.choice(list(self.development_domains.keys())),
            "scenario_type": random.choice(list(self.scenario_types.keys())),
            "context": {
                "setting": random.choice(["home", "clinic", "outdoor"]),
                "participants": ["caregiver", "infant"],
                "background": "日常互动中的观察和指导"
            },
            "infant_behavior": {
                "observable_signs": behaviors,
                "emotional_state": random.choice(list(self.emotion_mapping.keys())),
                "developmental_indicators": ["general_development"]
            },
            "guidance_dialogue": [
                {"speaker": "system", "content": f"观察到宝宝{behaviors[0]}", "guidance_type": "observation"},
                {"speaker": "caregiver", "content": "这个行为正常吗？", "guidance_type": "interpretation"},
                {"speaker": "system", "content": "这是该年龄段的典型表现", "guidance_type": "response"}
            ],
            "learning_objectives": ["观察婴儿行为", "理解发育特点"],
            "follow_up_suggestions": ["持续观察", "记录发展变化"]
        }

    def _topic_similarity(self, topic: str, key: str) -> bool:
        """简单的话题相似度判断"""
        return key in topic or any(word in topic for word in [key])

    def generate_dataset(self, original_data_path: str, output_path: str, target_size: int = 1000):
        """生成完整的婴幼儿数据集"""
        original_data = self.load_original_data(original_data_path)
        generated_data = []
        
        # 为每个年龄段生成数据
        samples_per_age = target_size // len(self.age_ranges)
        
        for age_range in self.age_ranges.keys():
            for i in range(samples_per_age):
                # 随机选择原始数据进行适配
                original_sample = random.choice(original_data)
                adapted_scenario = self.adapt_scenario_to_infant(original_sample, age_range)
                generated_data.append(adapted_scenario)
        
        # 保存生成的数据集
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(generated_data, f, ensure_ascii=False, indent=2)
        
        print(f"成功生成 {len(generated_data)} 个婴幼儿场景数据")
        print(f"数据已保存到: {output_path}")
        
        return generated_data

if __name__ == "__main__":
    generator = InfantEmotionDatasetGenerator()
    
    # 生成基于child_chat_data.json的婴幼儿数据集
    generator.generate_dataset(
        original_data_path="child_chat_data.json",
        output_path="infant_emotion_guidance_dataset.json",
        target_size=600  # 每个年龄段100个样本
    )

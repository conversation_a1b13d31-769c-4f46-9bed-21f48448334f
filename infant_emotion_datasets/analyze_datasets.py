#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集分析脚本
"""

import json
import os
from collections import Counter

def analyze_dataset(filename):
    """分析单个数据集"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list) or not data:
            return None
            
        sample = data[0]
        
        analysis = {
            'filename': filename,
            'total_samples': len(data),
            'size_mb': round(os.path.getsize(filename) / 1024 / 1024, 2),
            'sample_keys': list(sample.keys()),
            'data_type': 'unknown'
        }
        
        # 判断数据类型
        if 'target_age' in sample:
            analysis['data_type'] = 'qwen_enhanced'
            ages = [item.get('target_age', 'unknown') for item in data]
            analysis['age_distribution'] = dict(Counter(ages))
            
        elif 'age_range' in sample:
            analysis['data_type'] = 'structured_scenario'
            ages = [item.get('age_range', 'unknown') for item in data]
            domains = [item.get('development_domain', 'unknown') for item in data]
            analysis['age_distribution'] = dict(Counter(ages))
            analysis['domain_distribution'] = dict(Counter(domains))
            
        elif 'instruction' in sample:
            analysis['data_type'] = 'instruction_following'
            
        elif 'conversation' in sample:
            analysis['data_type'] = 'conversation'
            
        return analysis
        
    except Exception as e:
        print(f"Error analyzing {filename}: {e}")
        return None

def main():
    """主分析函数"""
    print("🔍 婴幼儿情绪发展数据集分析")
    print("=" * 50)
    
    datasets = []
    total_samples = 0
    total_size = 0
    
    for filename in sorted(os.listdir('.')):
        if filename.endswith('.json'):
            analysis = analyze_dataset(filename)
            if analysis:
                datasets.append(analysis)
                total_samples += analysis['total_samples']
                total_size += analysis['size_mb']
                
                print(f"\n📊 {filename}")
                print(f"   样本数: {analysis['total_samples']}")
                print(f"   大小: {analysis['size_mb']}MB")
                print(f"   类型: {analysis['data_type']}")
                
                if 'age_distribution' in analysis:
                    print(f"   年龄分布: {analysis['age_distribution']}")
                    
                if 'domain_distribution' in analysis:
                    print(f"   领域分布: {analysis['domain_distribution']}")
    
    print("\n" + "=" * 50)
    print(f"📈 总计: {len(datasets)} 个数据集")
    print(f"📊 总样本数: {total_samples}")
    print(f"💾 总大小: {total_size:.2f}MB")
    
    # 按类型分组
    type_groups = {}
    for dataset in datasets:
        data_type = dataset['data_type']
        if data_type not in type_groups:
            type_groups[data_type] = []
        type_groups[data_type].append(dataset)
    
    print(f"\n📋 数据集类型分布:")
    for data_type, group in type_groups.items():
        count = len(group)
        samples = sum(d['total_samples'] for d in group)
        print(f"   {data_type}: {count} 个数据集, {samples} 个样本")

if __name__ == "__main__":
    main()

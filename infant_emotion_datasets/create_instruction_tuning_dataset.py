#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿情绪发展数据集汇总与指令微调格式转换工具

将多个数据集汇总并转换为标准的 Instruction-Input-Output 三元组格式
适用于大模型微调训练
"""

import json
import os
from typing import List, Dict, Any
from datetime import datetime

class InstructionTuningDatasetCreator:
    """指令微调数据集创建器"""
    
    def __init__(self, dataset_dir: str = "."):
        self.dataset_dir = dataset_dir
        self.instruction_templates = {
            "scenario_guidance": [
                "请为这个婴幼儿发展场景提供专业的指导建议。",
                "根据以下婴幼儿行为观察，请提供专业的发展指导。",
                "请分析这个婴幼儿发展场景并给出指导策略。",
                "基于婴幼儿发展理论，请为以下场景提供专业建议。"
            ],
            "emotion_analysis": [
                "请分析这个婴幼儿的情绪状态并提供调节建议。",
                "根据婴幼儿的行为表现，请分析其情绪发展需求。",
                "请评估这个婴幼儿的社会情绪发展状况。"
            ],
            "parent_education": [
                "请为家长提供关于这个发展阶段的育儿指导。",
                "根据婴幼儿的发展特点，请给家长一些实用建议。",
                "请解释这个年龄段婴幼儿的发展特征和照护要点。"
            ],
            "professional_assessment": [
                "请从专业角度评估这个婴幼儿的发展状况。",
                "基于发展心理学理论，请分析这个案例。",
                "请提供这个发展场景的专业评估和建议。"
            ],
            "conversation": [
                "请回答关于婴幼儿发展的专业问题。",
                "作为婴幼儿发展专家，请回答以下咨询。",
                "请提供专业的婴幼儿照护指导。"
            ]
        }
    
    def load_json_dataset(self, filename: str) -> List[Dict]:
        """加载JSON数据集"""
        filepath = os.path.join(self.dataset_dir, filename)
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            return []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"✅ 成功加载 {filename}: {len(data)} 条数据")
                return data
        except Exception as e:
            print(f"❌ 加载 {filename} 失败: {e}")
            return []
    
    def convert_structured_scenario(self, data: List[Dict]) -> List[Dict]:
        """转换结构化场景数据"""
        results = []
        
        for item in data:
            # 基本信息提取
            age_range = item.get("age_range", "未知")
            domain = item.get("development_domain", "未知")
            behaviors = item.get("infant_behavior", {}).get("observable_signs", [])
            context = item.get("context", {}).get("background", "日常观察")
            
            # 构建输入
            input_text = f"年龄：{age_range}，发展领域：{domain}，场景：{context}，观察到的行为：{', '.join(behaviors)}"
            
            # 构建输出（从对话中提取关键指导）
            guidance_dialogue = item.get("guidance_dialogue", [])
            output_parts = []
            for dialogue in guidance_dialogue:
                if dialogue.get("guidance_type") in ["observation", "professional_insight", "response", "strategy"]:
                    output_parts.append(dialogue.get("content", ""))
            
            output_text = " ".join(output_parts) if output_parts else "需要进一步观察和评估"
            
            # 选择指令模板
            instruction = self.instruction_templates["scenario_guidance"][0]
            
            results.append({
                "instruction": instruction,
                "input": input_text,
                "output": output_text,
                "source": "structured_scenario",
                "age_range": age_range,
                "domain": domain
            })
        
        return results
    
    def convert_qwen_enhanced(self, data: List[Dict]) -> List[Dict]:
        """转换Qwen增强数据"""
        results = []
        
        for item in data:
            age_range = item.get("target_age", "未知")
            
            # 1. 情绪分析任务
            emotion_analysis = item.get("emotion_analysis", {})
            if emotion_analysis:
                input_text = f"年龄：{age_range}，原始话题：{item.get('original_topic', '')}，需要分析婴幼儿的情绪发展特点"
                
                # 构建情绪分析输出
                emotion_output = []
                if "情感识别与变化" in emotion_analysis:
                    emotion_info = emotion_analysis["情感识别与变化"]
                    emotion_output.append(f"情绪状态：{emotion_info.get('孩子的初始情绪状态', '')}")
                    emotion_output.append(f"情绪变化：{emotion_info.get('最终的情绪状态', '')}")
                
                if "指导策略分析" in emotion_analysis:
                    strategy_info = emotion_analysis["指导策略分析"]
                    techniques = strategy_info.get("情绪指导技巧", [])
                    if techniques:
                        # 处理可能的字典或字符串格式
                        technique_strs = []
                        for tech in techniques[:3]:
                            if isinstance(tech, dict):
                                technique_strs.append(str(tech))
                            else:
                                technique_strs.append(str(tech))
                        emotion_output.append(f"指导策略：{'; '.join(technique_strs)}")
                
                results.append({
                    "instruction": self.instruction_templates["emotion_analysis"][0],
                    "input": input_text,
                    "output": " ".join(emotion_output),
                    "source": "qwen_enhanced_emotion",
                    "age_range": age_range,
                    "domain": "emotion"
                })
            
            # 2. 婴幼儿场景指导任务
            infant_scenario = item.get("infant_scenario", {})
            if infant_scenario:
                scenario_bg = infant_scenario.get("场景背景描述", "")
                behaviors = infant_scenario.get("婴幼儿的具体行为表现", {})
                
                # 安全处理behaviors字典
                behavior_str = ""
                if isinstance(behaviors, dict):
                    behavior_parts = []
                    for key, value in behaviors.items():
                        behavior_parts.append(f"{key}:{value}")
                    behavior_str = ", ".join(behavior_parts)
                else:
                    behavior_str = str(behaviors)

                input_text = f"年龄：{age_range}，场景：{scenario_bg}，行为表现：{behavior_str}"
                
                guidance = infant_scenario.get("专业的指导建议和策略", {})
                output_parts = []
                if isinstance(guidance, dict):
                    for key, value in guidance.items():
                        if isinstance(value, str):
                            output_parts.append(f"{key}：{value}")
                elif isinstance(guidance, str):
                    output_parts.append(guidance)
                
                results.append({
                    "instruction": self.instruction_templates["scenario_guidance"][1],
                    "input": input_text,
                    "output": " ".join(output_parts),
                    "source": "qwen_enhanced_scenario",
                    "age_range": age_range,
                    "domain": "comprehensive"
                })
        
        return results
    
    def convert_conversation_data(self, data: List[Dict]) -> List[Dict]:
        """转换对话数据"""
        results = []
        
        for item in data:
            conversation = item.get("conversation", [])
            age_range = item.get("age_range", "未知")
            
            if len(conversation) >= 2:
                user_msg = conversation[0].get("content", "")
                assistant_msg = conversation[1].get("content", "")
                
                results.append({
                    "instruction": self.instruction_templates["conversation"][0],
                    "input": user_msg,
                    "output": assistant_msg,
                    "source": "conversation",
                    "age_range": age_range,
                    "domain": "consultation"
                })
        
        return results
    
    def convert_existing_instruction_data(self, data: List[Dict]) -> List[Dict]:
        """转换已有的指令数据（保持原格式）"""
        results = []
        
        for item in data:
            if all(key in item for key in ["instruction", "input", "output"]):
                # 添加元数据
                enhanced_item = item.copy()
                enhanced_item["source"] = "existing_instruction"
                
                # 尝试从input中提取年龄信息
                input_text = item.get("input", "")
                age_range = "未知"
                if "年龄：" in input_text:
                    age_part = input_text.split("年龄：")[1].split("，")[0]
                    age_range = age_part
                
                enhanced_item["age_range"] = age_range
                enhanced_item["domain"] = "general"
                
                results.append(enhanced_item)
        
        return results
    
    def create_comprehensive_dataset(self) -> List[Dict]:
        """创建综合指令微调数据集"""
        all_data = []
        
        # 数据集文件映射
        dataset_files = {
            "enhanced_infant_emotion_dataset.json": self.convert_structured_scenario,
            "qwen_enhanced_infant_emotion_dataset.json": self.convert_qwen_enhanced,
            "infant_guidance_conversation_data.json": self.convert_conversation_data,
            "infant_guidance_instruction_data.json": self.convert_existing_instruction_data,
            "emotion_coaching_dataset.json": self.convert_structured_scenario,
            "parent_education_dataset.json": self.convert_structured_scenario,
            "professional_training_dataset.json": self.convert_structured_scenario,
            "developmental_assessment_dataset.json": self.convert_structured_scenario
        }
        
        # 处理每个数据集
        for filename, converter_func in dataset_files.items():
            print(f"\n📂 处理数据集: {filename}")
            raw_data = self.load_json_dataset(filename)
            
            if raw_data:
                converted_data = converter_func(raw_data)
                all_data.extend(converted_data)
                print(f"✅ 转换完成: {len(converted_data)} 条指令数据")
        
        return all_data
    
    def generate_statistics(self, data: List[Dict]) -> Dict:
        """生成数据集统计信息"""
        stats = {
            "total_samples": len(data),
            "by_source": {},
            "by_age_range": {},
            "by_domain": {},
            "instruction_types": {}
        }
        
        for item in data:
            # 按来源统计
            source = item.get("source", "unknown")
            stats["by_source"][source] = stats["by_source"].get(source, 0) + 1
            
            # 按年龄段统计
            age_range = item.get("age_range", "unknown")
            stats["by_age_range"][age_range] = stats["by_age_range"].get(age_range, 0) + 1
            
            # 按领域统计
            domain = item.get("domain", "unknown")
            stats["by_domain"][domain] = stats["by_domain"].get(domain, 0) + 1
            
            # 按指令类型统计
            instruction = item.get("instruction", "")
            for template_type, templates in self.instruction_templates.items():
                if instruction in templates:
                    stats["instruction_types"][template_type] = stats["instruction_types"].get(template_type, 0) + 1
                    break
        
        return stats
    
    def save_dataset(self, data: List[Dict], filename: str = "infant_emotion_instruction_tuning_dataset.json"):
        """保存指令微调数据集"""
        output_path = os.path.join(self.dataset_dir, filename)
        
        # 添加元数据
        dataset_with_metadata = {
            "metadata": {
                "dataset_name": "婴幼儿社会情绪发展指导指令微调数据集",
                "version": "1.0",
                "created_date": datetime.now().isoformat(),
                "description": "基于多个婴幼儿发展数据集汇总的指令微调训练数据",
                "total_samples": len(data),
                "format": "instruction-input-output"
            },
            "data": data
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_with_metadata, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 数据集已保存到: {output_path}")
        return output_path

def main():
    """主函数"""
    print("🚀 开始创建婴幼儿情绪发展指令微调数据集")
    
    # 创建数据集创建器
    creator = InstructionTuningDatasetCreator()
    
    # 生成综合数据集
    print("\n📊 汇总所有数据集...")
    instruction_data = creator.create_comprehensive_dataset()
    
    # 生成统计信息
    print("\n📈 生成统计信息...")
    stats = creator.generate_statistics(instruction_data)
    
    # 打印统计结果
    print(f"\n📋 数据集统计:")
    print(f"总样本数: {stats['total_samples']}")
    print(f"\n按来源分布:")
    for source, count in stats['by_source'].items():
        print(f"  {source}: {count}")
    print(f"\n按年龄段分布:")
    for age, count in stats['by_age_range'].items():
        print(f"  {age}: {count}")
    print(f"\n按领域分布:")
    for domain, count in stats['by_domain'].items():
        print(f"  {domain}: {count}")
    
    # 保存数据集
    print("\n💾 保存数据集...")
    output_file = creator.save_dataset(instruction_data)
    
    # 保存统计信息
    stats_file = os.path.join(creator.dataset_dir, "instruction_tuning_statistics.json")
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"📊 统计信息已保存到: {stats_file}")
    print(f"\n🎉 指令微调数据集创建完成！")
    print(f"📁 输出文件: {output_file}")
    print(f"📊 统计文件: {stats_file}")

if __name__ == "__main__":
    main()

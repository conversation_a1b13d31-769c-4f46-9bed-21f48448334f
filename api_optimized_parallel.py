#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API优化的并行处理器 - 专门针对API调用限制优化的并行方案
"""

import json
import time
import random
import asyncio
import aiohttp
import threading
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import requests

class APIOptimizedParallel:
    """API优化的并行处理器"""
    
    def __init__(self, api_key: str, model: str = "qwen-turbo"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # API调用控制
        self.api_semaphore = threading.Semaphore(3)  # 最多3个并发API调用
        self.call_interval = 0.5  # API调用间隔
        self.last_call_time = 0
        self.call_lock = threading.Lock()
        
        # 统计信息
        self.total_api_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        
    def controlled_api_call(self, prompt: str) -> str:
        """受控的API调用 - 限制并发和频率"""
        
        with self.api_semaphore:  # 限制并发数
            with self.call_lock:  # 控制调用频率
                current_time = time.time()
                time_since_last = current_time - self.last_call_time
                
                if time_since_last < self.call_interval:
                    sleep_time = self.call_interval - time_since_last
                    time.sleep(sleep_time)
                
                self.last_call_time = time.time()
            
            # 执行API调用
            return self._make_api_request(prompt)
    
    def _make_api_request(self, prompt: str) -> str:
        """执行实际的API请求"""
        
        payload = {
            "model": self.model,
            "input": {
                "messages": [{"role": "user", "content": prompt}]
            },
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 1500
            }
        }
        
        try:
            self.total_api_calls += 1
            response = requests.post(self.base_url, headers=self.headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if "output" in result and "text" in result["output"]:
                self.successful_calls += 1
                return result["output"]["text"]
            else:
                self.failed_calls += 1
                return None
                
        except Exception as e:
            self.failed_calls += 1
            print(f"API调用失败: {e}")
            return None
    
    def process_sample_optimized(self, sample: Dict, target_age: str, worker_id: str) -> Dict:
        """优化的样本处理 - 减少API调用次数"""
        
        try:
            topic = sample.get("topic", "未知")
            dialogue_text = sample.get("input", "").replace("</s>", "\n")
            
            print(f"🔄 Worker-{worker_id}: 处理 {topic}")
            
            # 合并提示词 - 一次API调用完成所有分析
            combined_prompt = f"""
请对以下对话进行全面分析，并适配为{target_age}婴幼儿场景：

原始对话：
{dialogue_text}

请提供以下分析（JSON格式）：
1. 情感分析：情绪变化轨迹、触发因素、指导策略
2. 婴幼儿场景适配：适合{target_age}的场景、行为表现、指导方法
3. 专业增强：理论基础、评估维度、干预策略

请确保内容适合{target_age}年龄段的发育特点。
"""
            
            # 单次API调用获取所有分析
            api_response = self.controlled_api_call(combined_prompt)
            
            if api_response:
                # 尝试解析JSON响应
                try:
                    # 提取JSON部分
                    json_start = api_response.find('{')
                    json_end = api_response.rfind('}') + 1
                    if json_start != -1 and json_end != -1:
                        json_str = api_response[json_start:json_end]
                        analysis_result = json.loads(json_str)
                    else:
                        # 如果无法解析JSON，使用规则生成
                        analysis_result = self._create_fallback_analysis(topic, target_age)
                except:
                    analysis_result = self._create_fallback_analysis(topic, target_age)
            else:
                # API失败，使用规则生成
                analysis_result = self._create_fallback_analysis(topic, target_age)
            
            # 构建最终结果
            result = {
                "scenario_id": f"api_optimized_{target_age}_{random.randint(1000, 9999)}",
                "original_topic": topic,
                "target_age": target_age,
                "analysis_result": analysis_result,
                "generation_method": "api_optimized_single_call",
                "worker_id": worker_id,
                "timestamp": time.time()
            }
            
            print(f"✅ Worker-{worker_id}: 完成 {result['scenario_id']}")
            return result
            
        except Exception as e:
            print(f"❌ Worker-{worker_id}: 处理失败 {e}")
            return None
    
    def _create_fallback_analysis(self, topic: str, target_age: str) -> Dict:
        """备用分析 - 当API失败时使用"""
        
        age_behaviors = {
            "0-6m": ["哭闹", "微笑", "注视", "抓握"],
            "6-12m": ["坐立", "爬行", "拍手", "咿呀学语"],
            "12-18m": ["走路", "指认", "简单词汇", "模仿"],
            "18-24m": ["跑跳", "两词句", "自我意识", "探索"],
            "24-36m": ["完整句子", "角色游戏", "情绪调节", "同伴互动"]
        }
        
        behaviors = random.sample(age_behaviors.get(target_age, ["一般行为"]), 2)
        
        return {
            "emotion_analysis": {
                "emotion_trajectory": f"与{topic}相关的情绪变化",
                "guidance_strategies": ["观察", "理解", "回应", "引导"]
            },
            "infant_scenario": {
                "scenario_background": f"{target_age}婴幼儿在{topic}情境中的表现",
                "infant_behaviors": behaviors,
                "caregiver_guidance": ["及时回应", "提供支持", "适当引导"]
            },
            "professional_enhancement": {
                "theoretical_basis": f"{target_age}阶段发展特点",
                "assessment_dimensions": ["情绪发展", "社会交往", "认知能力"],
                "intervention_strategies": ["回应性照护", "发展支持", "环境适应"]
            }
        }
    
    def parallel_process_with_api_control(self, input_file: str, output_file: str,
                                        target_ages: List[str], max_samples: int = 100,
                                        max_workers: int = 6):
        """带API控制的并行处理"""
        
        print(f"🚀 API优化并行处理")
        print(f"最大并发数: {max_workers}")
        print(f"API并发限制: 3")
        print(f"API调用间隔: {self.call_interval}秒")
        
        # 加载数据
        with open(input_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 准备任务
        tasks = []
        samples_per_age = max_samples // len(target_ages)
        
        for age_idx, age in enumerate(target_ages):
            selected_samples = random.sample(original_data, min(samples_per_age, len(original_data)))
            for sample_idx, sample in enumerate(selected_samples):
                worker_id = f"{age_idx}-{sample_idx}"
                tasks.append((sample, age, worker_id))
        
        print(f"准备处理 {len(tasks)} 个任务")
        
        # 并行执行
        results = []
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_task = {
                executor.submit(self.process_sample_optimized, task[0], task[1], task[2]): task 
                for task in tasks
            }
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_task):
                result = future.result()
                if result:
                    results.append(result)
                
                completed += 1
                if completed % 5 == 0:  # 每5个任务显示一次进度
                    elapsed = time.time() - start_time
                    avg_time = elapsed / completed
                    remaining = len(tasks) - completed
                    eta = remaining * avg_time
                    
                    print(f"📊 进度: {completed}/{len(tasks)} "
                          f"({completed/len(tasks)*100:.1f}%) "
                          f"| ETA: {eta/60:.1f}分钟 "
                          f"| API成功率: {self.successful_calls}/{self.total_api_calls}")
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        total_time = time.time() - start_time
        
        print(f"\n✅ API优化并行处理完成！")
        print(f"总耗时: {total_time/60:.1f}分钟")
        print(f"成功处理: {len(results)}/{len(tasks)}")
        print(f"平均速度: {total_time/len(results):.1f}秒/样本")
        print(f"API调用统计: 成功{self.successful_calls}, 失败{self.failed_calls}, 总计{self.total_api_calls}")
        print(f"API成功率: {self.successful_calls/self.total_api_calls*100:.1f}%")
        print(f"结果保存到: {output_file}")
        
        return results

def demo_api_optimized():
    """API优化并行处理演示"""
    
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    processor = APIOptimizedParallel(API_KEY, model="qwen-turbo")
    
    # 先小规模测试
    print("🧪 小规模测试 (10个样本)")
    
    results = processor.parallel_process_with_api_control(
        input_file="child_chat_data.json",
        output_file="api_optimized_test.json",
        target_ages=["12-18m"],
        max_samples=10,
        max_workers=4
    )
    
    if len(results) > 0:
        print("\n🎯 测试成功！可以进行大规模处理")
        
        # 询问是否继续大规模处理
        continue_choice = input("是否继续处理更多样本？(y/n): ").strip().lower()
        
        if continue_choice == 'y':
            batch_size = int(input("输入批次大小 (建议50-100): ") or "50")
            
            processor_large = APIOptimizedParallel(API_KEY, model="qwen-turbo")
            
            processor_large.parallel_process_with_api_control(
                input_file="child_chat_data.json",
                output_file="api_optimized_large.json",
                target_ages=["12-18m", "18-24m", "24-36m"],
                max_samples=batch_size,
                max_workers=6
            )

if __name__ == "__main__":
    demo_api_optimized()

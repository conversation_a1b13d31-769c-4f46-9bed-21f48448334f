#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理器 - 使用多线程/多进程加速数据集构建
"""

import json
import time
import random
import threading
import concurrent.futures
from typing import List, Dict, Any
from queue import Queue
import multiprocessing as mp
from qwen_enhanced_dataset_builder import EmotionGuidanceDatasetBuilder

class ParallelProcessor:
    """并行处理器"""
    
    def __init__(self, api_key: str, max_workers: int = 4, model: str = "qwen-turbo"):
        self.api_key = api_key
        self.max_workers = max_workers
        self.model = model
        self.results_queue = Queue()
        self.processed_count = 0
        self.total_count = 0
        self.start_time = None
        
    def process_single_sample(self, sample_data: tuple) -> Dict:
        """处理单个样本 - 线程安全"""
        sample, target_age, worker_id = sample_data
        
        try:
            # 每个线程创建自己的构建器实例
            builder = EmotionGuidanceDatasetBuilder(
                self.api_key, 
                fast_mode=True, 
                model=self.model
            )
            
            print(f"🔄 Worker-{worker_id}: 处理 {sample.get('topic', '未知')}")
            
            result = builder.process_dialogue_sample(sample, target_age)
            
            if result:
                print(f"✅ Worker-{worker_id}: 完成 {result['scenario_id']}")
                return result
            else:
                print(f"❌ Worker-{worker_id}: 处理失败")
                return None
                
        except Exception as e:
            print(f"❌ Worker-{worker_id}: 异常 {e}")
            return None
    
    def update_progress(self):
        """更新进度显示"""
        self.processed_count += 1
        if self.start_time:
            elapsed = time.time() - self.start_time
            if self.processed_count > 0:
                avg_time = elapsed / self.processed_count
                remaining = self.total_count - self.processed_count
                eta = remaining * avg_time
                
                print(f"📊 进度: {self.processed_count}/{self.total_count} "
                      f"({self.processed_count/self.total_count*100:.1f}%) "
                      f"| 预计剩余: {eta/60:.1f}分钟")
    
    def parallel_process_threading(self, input_file: str, output_file: str, 
                                 target_ages: List[str], max_samples: int = 100):
        """使用多线程并行处理"""
        
        print(f"🚀 多线程并行处理 (线程数: {self.max_workers})")
        
        # 加载数据
        with open(input_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 准备任务
        tasks = []
        samples_per_age = max_samples // len(target_ages)
        
        for age_idx, age in enumerate(target_ages):
            selected_samples = random.sample(original_data, min(samples_per_age, len(original_data)))
            for sample_idx, sample in enumerate(selected_samples):
                worker_id = f"{age_idx}-{sample_idx}"
                tasks.append((sample, age, worker_id))
        
        self.total_count = len(tasks)
        self.start_time = time.time()
        
        print(f"准备处理 {self.total_count} 个任务")
        
        # 并行执行
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_task = {executor.submit(self.process_single_sample, task): task for task in tasks}
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_task):
                result = future.result()
                if result:
                    results.append(result)
                self.update_progress()
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        total_time = time.time() - self.start_time
        print(f"\n✅ 并行处理完成！")
        print(f"总耗时: {total_time/60:.1f}分钟")
        print(f"成功处理: {len(results)}/{self.total_count}")
        print(f"平均速度: {total_time/len(results):.1f}秒/样本")
        print(f"结果保存到: {output_file}")
        
        return results
    
    def parallel_process_multiprocessing(self, input_file: str, output_file: str,
                                       target_ages: List[str], max_samples: int = 100):
        """使用多进程并行处理 (更适合CPU密集型任务)"""
        
        print(f"🚀 多进程并行处理 (进程数: {self.max_workers})")
        
        # 加载数据
        with open(input_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 准备任务
        tasks = []
        samples_per_age = max_samples // len(target_ages)
        
        for age_idx, age in enumerate(target_ages):
            selected_samples = random.sample(original_data, min(samples_per_age, len(original_data)))
            for sample_idx, sample in enumerate(selected_samples):
                worker_id = f"{age_idx}-{sample_idx}"
                tasks.append((sample, age, worker_id))
        
        self.total_count = len(tasks)
        self.start_time = time.time()
        
        print(f"准备处理 {self.total_count} 个任务")
        
        # 并行执行
        with mp.Pool(processes=self.max_workers) as pool:
            results = pool.map(self.process_single_sample_mp, tasks)
        
        # 过滤有效结果
        valid_results = [r for r in results if r is not None]
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(valid_results, f, ensure_ascii=False, indent=2)
        
        total_time = time.time() - self.start_time
        print(f"\n✅ 并行处理完成！")
        print(f"总耗时: {total_time/60:.1f}分钟")
        print(f"成功处理: {len(valid_results)}/{self.total_count}")
        print(f"平均速度: {total_time/len(valid_results):.1f}秒/样本")
        print(f"结果保存到: {output_file}")
        
        return valid_results
    
    def process_single_sample_mp(self, sample_data: tuple) -> Dict:
        """多进程版本的单样本处理"""
        sample, target_age, worker_id = sample_data
        
        try:
            # 每个进程创建自己的构建器实例
            builder = EmotionGuidanceDatasetBuilder(
                self.api_key, 
                fast_mode=True, 
                model=self.model
            )
            
            result = builder.process_dialogue_sample(sample, target_age)
            return result
                
        except Exception as e:
            print(f"❌ Process-{worker_id}: 异常 {e}")
            return None

class SmartParallelProcessor:
    """智能并行处理器 - 自动调节并发数和API调用频率"""
    
    def __init__(self, api_key: str, model: str = "qwen-turbo"):
        self.api_key = api_key
        self.model = model
        self.api_call_times = []
        self.optimal_workers = 2  # 初始并发数
        
    def adaptive_process(self, input_file: str, output_file: str, 
                        target_ages: List[str], max_samples: int = 100):
        """自适应并行处理"""
        
        print("🧠 智能自适应并行处理")
        
        # 第一阶段：测试最优并发数
        print("📊 第一阶段：测试最优并发数")
        self.find_optimal_concurrency(input_file, target_ages)
        
        # 第二阶段：使用最优配置处理全部数据
        print(f"📊 第二阶段：使用 {self.optimal_workers} 个并发处理全部数据")
        
        processor = ParallelProcessor(
            self.api_key, 
            max_workers=self.optimal_workers, 
            model=self.model
        )
        
        return processor.parallel_process_threading(
            input_file, output_file, target_ages, max_samples
        )
    
    def find_optimal_concurrency(self, input_file: str, target_ages: List[str]):
        """寻找最优并发数"""
        
        test_configs = [
            {"workers": 1, "samples": 3},
            {"workers": 2, "samples": 4}, 
            {"workers": 4, "samples": 8},
            {"workers": 6, "samples": 12}
        ]
        
        best_speed = 0
        best_workers = 2
        
        for config in test_configs:
            print(f"🧪 测试 {config['workers']} 个并发...")
            
            processor = ParallelProcessor(
                self.api_key,
                max_workers=config['workers'],
                model=self.model
            )
            
            start_time = time.time()
            
            try:
                results = processor.parallel_process_threading(
                    input_file,
                    f"test_parallel_{config['workers']}.json",
                    target_ages[:1],  # 只测试一个年龄段
                    config['samples']
                )
                
                elapsed = time.time() - start_time
                speed = len(results) / elapsed if elapsed > 0 else 0
                
                print(f"  {config['workers']} 并发: {speed:.2f} 样本/秒")
                
                if speed > best_speed:
                    best_speed = speed
                    best_workers = config['workers']
                    
            except Exception as e:
                print(f"  {config['workers']} 并发测试失败: {e}")
        
        self.optimal_workers = best_workers
        print(f"🎯 最优并发数: {best_workers} (速度: {best_speed:.2f} 样本/秒)")

def demo_parallel_processing():
    """并行处理演示"""
    
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    print("🚀 并行处理演示")
    print("选择处理模式:")
    print("1. 多线程并行 (推荐，适合IO密集型)")
    print("2. 多进程并行 (适合CPU密集型)")
    print("3. 智能自适应并行 (自动优化)")
    
    choice = input("请选择 (1/2/3): ").strip() or "1"
    
    if choice == "1":
        # 多线程并行
        processor = ParallelProcessor(
            API_KEY, 
            max_workers=4,  # 可以调整并发数
            model="qwen-turbo"
        )
        
        processor.parallel_process_threading(
            input_file="child_chat_data.json",
            output_file="parallel_threading_result.json",
            target_ages=["12-18m", "18-24m"],
            max_samples=20  # 先测试20个样本
        )
        
    elif choice == "2":
        # 多进程并行
        processor = ParallelProcessor(
            API_KEY,
            max_workers=2,  # 进程数通常设置为CPU核心数
            model="qwen-turbo"
        )
        
        processor.parallel_process_multiprocessing(
            input_file="child_chat_data.json", 
            output_file="parallel_multiprocessing_result.json",
            target_ages=["12-18m", "18-24m"],
            max_samples=20
        )
        
    elif choice == "3":
        # 智能自适应
        processor = SmartParallelProcessor(API_KEY, model="qwen-turbo")
        
        processor.adaptive_process(
            input_file="child_chat_data.json",
            output_file="smart_parallel_result.json", 
            target_ages=["12-18m", "18-24m"],
            max_samples=30
        )

if __name__ == "__main__":
    demo_parallel_processing()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿社会情绪发展指导数据集增强器
利用LLM和专业知识库来丰富和完善数据集内容
"""

import json
import random
from typing import Dict, List, Any

class InfantDatasetEnhancer:
    def __init__(self):
        # 基于ASQ:SE-2和专业指南的发育里程碑
        self.developmental_milestones = {
            "0-1m": {
                "social_emotional": [
                    "对熟悉声音有反应",
                    "被抱时能安静下来",
                    "开始有眼神接触",
                    "对人脸表现出兴趣"
                ],
                "communication": [
                    "用哭声表达需求",
                    "对声音有反应",
                    "开始发出咕咕声"
                ],
                "motor": [
                    "具有原始反射",
                    "头部略能抬起",
                    "手脚活动"
                ]
            },
            "1-6m": {
                "social_emotional": [
                    "社交性微笑",
                    "认识熟悉的人",
                    "喜欢与人互动",
                    "表现出不同情绪"
                ],
                "communication": [
                    "咿呀学语",
                    "对名字有反应",
                    "模仿声音",
                    "用声音吸引注意"
                ],
                "motor": [
                    "抬头稳定",
                    "翻身",
                    "伸手抓物",
                    "坐立（有支撑）"
                ]
            },
            "6-12m": {
                "social_emotional": [
                    "陌生人焦虑",
                    "分离焦虑",
                    "模仿他人行为",
                    "表现出偏好"
                ],
                "communication": [
                    "理解简单指令",
                    "说出第一个词",
                    "使用手势",
                    "模仿语音"
                ],
                "motor": [
                    "独立坐立",
                    "爬行",
                    "拉着站立",
                    "精细抓握"
                ]
            },
            "12-18m": {
                "social_emotional": [
                    "表现出独立性",
                    "模仿日常活动",
                    "寻求关注",
                    "表现出同情心"
                ],
                "communication": [
                    "词汇量增长",
                    "理解简单问题",
                    "使用手势交流",
                    "跟随简单指令"
                ],
                "motor": [
                    "独立行走",
                    "爬楼梯",
                    "用勺子吃饭",
                    "搭积木"
                ]
            },
            "18-24m": {
                "social_emotional": [
                    "平行游戏",
                    "情绪爆发",
                    "寻求安慰",
                    "表现出骄傲"
                ],
                "communication": [
                    "两词组合",
                    "提问",
                    "命名物品",
                    "跟唱简单歌曲"
                ],
                "motor": [
                    "跑步",
                    "踢球",
                    "翻书页",
                    "画线条"
                ]
            },
            "24-36m": {
                "social_emotional": [
                    "与他人游戏",
                    "表现出关心",
                    "理解情绪",
                    "开始分享"
                ],
                "communication": [
                    "完整句子",
                    "讲述经历",
                    "理解复杂指令",
                    "唱完整歌曲"
                ],
                "motor": [
                    "跳跃",
                    "骑三轮车",
                    "使用剪刀",
                    "画圆形"
                ]
            }
        }
        
        # 常见问题行为和指导策略
        self.problem_behaviors = {
            "excessive_crying": {
                "description": "过度哭闹",
                "possible_causes": ["饥饿", "疲劳", "不适", "过度刺激", "疾病"],
                "strategies": ["检查基本需求", "创造安静环境", "使用安抚技巧", "寻求医疗建议"]
            },
            "sleep_difficulties": {
                "description": "睡眠困难",
                "possible_causes": ["睡眠环境", "作息不规律", "过度兴奋", "分离焦虑"],
                "strategies": ["建立睡前程序", "调整环境", "渐进式训练", "保持一致性"]
            },
            "feeding_problems": {
                "description": "喂养问题",
                "possible_causes": ["口腔敏感", "食物过敏", "发育阶段", "环境干扰"],
                "strategies": ["观察反应", "调整质地", "创造愉快氛围", "咨询专业人士"]
            },
            "separation_anxiety": {
                "description": "分离焦虑",
                "possible_causes": ["依恋发展", "陌生环境", "突然变化", "缺乏安全感"],
                "strategies": ["渐进分离", "过渡物品", "保持冷静", "建立信任"]
            }
        }
        
        # 专业指导模板
        self.guidance_templates = {
            "observation": [
                "观察到宝宝{behavior}，这可能表示{meaning}",
                "宝宝表现出{behavior}，这是{age}阶段的{type}表现",
                "注意到宝宝{behavior}，需要关注{aspect}"
            ],
            "interpretation": [
                "这种行为通常表明{explanation}",
                "从发育角度来看，{interpretation}",
                "这可能与{factor}有关"
            ],
            "strategy": [
                "建议采用{method}来{goal}",
                "可以尝试{approach}，有助于{benefit}",
                "通过{technique}来支持宝宝的{development}"
            ],
            "follow_up": [
                "继续观察{aspect}的变化",
                "记录{behavior}的模式和频率",
                "如果{condition}，建议咨询专业人士"
            ]
        }

    def enhance_scenario(self, scenario: Dict) -> Dict:
        """增强单个场景的专业性和详细程度"""
        age = scenario["age_range"]
        domain = scenario["development_domain"]
        
        # 添加发育里程碑信息
        if age in self.developmental_milestones:
            milestones = self.developmental_milestones[age]
            if domain in milestones:
                scenario["developmental_context"] = {
                    "expected_milestones": milestones[domain],
                    "age_appropriate_behaviors": random.sample(milestones[domain], min(2, len(milestones[domain])))
                }
        
        # 增强指导对话
        enhanced_dialogue = []
        for dialogue in scenario["guidance_dialogue"]:
            enhanced_dialogue.append(dialogue)
            
            # 添加更详细的专业解释
            if dialogue["guidance_type"] == "observation":
                enhanced_dialogue.append({
                    "speaker": "system",
                    "content": self._generate_professional_insight(scenario, "observation"),
                    "guidance_type": "professional_insight"
                })
            elif dialogue["guidance_type"] == "strategy":
                enhanced_dialogue.append({
                    "speaker": "system", 
                    "content": self._generate_specific_technique(scenario),
                    "guidance_type": "specific_technique"
                })
        
        scenario["guidance_dialogue"] = enhanced_dialogue
        
        # 添加评估维度
        scenario["assessment_dimensions"] = self._generate_assessment_dimensions(scenario)
        
        # 添加家长教育要点
        scenario["parent_education"] = self._generate_parent_education(scenario)
        
        return scenario

    def _generate_professional_insight(self, scenario: Dict, context: str) -> str:
        """生成专业见解"""
        age = scenario["age_range"]
        behaviors = scenario["infant_behavior"]["observable_signs"]
        
        insights = [
            f"从发育心理学角度，{age}阶段的宝宝{behaviors[0]}是正常的发展表现",
            f"这种行为反映了{age}婴幼儿的神经系统发育特点",
            f"根据依恋理论，宝宝的{behaviors[0]}行为有助于建立安全依恋关系"
        ]
        
        return random.choice(insights)

    def _generate_specific_technique(self, scenario: Dict) -> str:
        """生成具体技巧"""
        domain = scenario["development_domain"]
        
        techniques = {
            "emotion": [
                "使用'情绪镜像'技巧，反映宝宝的情绪状态",
                "采用'共情回应'，让宝宝感受到被理解",
                "运用'情绪命名'，帮助宝宝识别情绪"
            ],
            "social": [
                "创造'安全基地'，让宝宝有安全感去探索",
                "使用'回应性互动'，及时回应宝宝的社交信号",
                "提供'社交脚手架'，支持社交技能发展"
            ],
            "cognitive": [
                "运用'引导发现'，让宝宝主动探索",
                "提供'适度挑战'，促进认知发展",
                "使用'重复与变化'，巩固学习效果"
            ]
        }
        
        return random.choice(techniques.get(domain, techniques["emotion"]))

    def _generate_assessment_dimensions(self, scenario: Dict) -> List[str]:
        """生成评估维度"""
        base_dimensions = [
            "行为观察准确性",
            "情绪识别能力", 
            "回应适宜性",
            "发育理解程度"
        ]
        
        domain_specific = {
            "emotion": ["情绪调节支持", "共情回应质量"],
            "social": ["社交互动促进", "依恋关系建立"],
            "cognitive": ["认知刺激提供", "学习机会创造"],
            "motor": ["运动发展支持", "安全环境创造"],
            "language": ["语言输入质量", "交流机会提供"]
        }
        
        domain = scenario["development_domain"]
        return base_dimensions + domain_specific.get(domain, [])

    def _generate_parent_education(self, scenario: Dict) -> Dict:
        """生成家长教育要点"""
        age = scenario["age_range"]
        
        education_points = {
            "key_concepts": [
                f"{age}阶段的发育特点",
                "观察和记录的重要性",
                "回应性照护的原则"
            ],
            "practical_tips": [
                "建立规律的日常程序",
                "创造丰富的互动机会", 
                "保持耐心和一致性"
            ],
            "warning_signs": [
                "发育迟缓的早期信号",
                "需要专业评估的情况",
                "何时寻求帮助"
            ]
        }
        
        return education_points

    def enhance_dataset(self, input_path: str, output_path: str):
        """增强整个数据集"""
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        enhanced_data = []
        for scenario in data:
            enhanced_scenario = self.enhance_scenario(scenario)
            enhanced_data.append(enhanced_scenario)
        
        # 保存增强后的数据集
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
        
        print(f"成功增强 {len(enhanced_data)} 个场景")
        print(f"增强后的数据已保存到: {output_path}")
        
        return enhanced_data

if __name__ == "__main__":
    enhancer = InfantDatasetEnhancer()
    
    # 增强基础数据集
    enhancer.enhance_dataset(
        input_path="infant_emotion_guidance_dataset.json",
        output_path="enhanced_infant_emotion_dataset.json"
    )

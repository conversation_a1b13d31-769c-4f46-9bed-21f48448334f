# 基于Qwen API的婴幼儿社会情绪发展数据集构建项目

## 🎯 项目概述

本项目基于您现有的5000条PEC（Parental Emotion Coaching）中文对话数据，结合Qwen API的强大分析能力，构建专业的婴幼儿（0-3岁）社会情绪发展指导数据集。

### 核心优势
- **深度分析**: 利用Qwen API进行情感轨迹分析和指导策略提取
- **专业适配**: 将学龄儿童场景转换为婴幼儿发育阶段场景
- **理论支撑**: 融入ASQ:SE-2、依恋理论、发育心理学等专业知识
- **实用性强**: 生成可直接用于LLM训练的高质量数据

## 📊 数据处理流程

### 输入数据格式
您的数据格式：
```json
{
  "topic": "去动物园",
  "input": "孩子：哇，终于到动物园了！我好激动啊！</s>智能助手：我听到你的声音中带有很多兴奋和激动..."
}
```

### 处理步骤

#### 1. 情感分析 (Qwen API)
- **情感轨迹**: 分析孩子从开始到结束的情绪变化
- **触发因素**: 识别导致情绪变化的关键因素  
- **指导策略**: 提取智能助手使用的情绪指导技巧
- **效果评估**: 分析指导的有效性指标

#### 2. 婴幼儿场景适配 (Qwen API)
- **年龄特点**: 根据0-3岁发育特点调整场景
- **表达方式**: 从语言表达转为行为表达（哭闹、肢体动作等）
- **场景转换**: 调整为典型的婴幼儿生活场景
- **指导调整**: 转换为适合养育者的观察和回应策略

#### 3. 专业知识增强 (Qwen API)
- **理论基础**: 融入依恋理论、情绪指导理论等
- **评估维度**: 基于ASQ:SE-2等标准化工具
- **干预策略**: 提供循证的早期干预方法
- **发展目标**: 明确的发展性指导目标

### 输出数据结构
```json
{
  "scenario_id": "qwen_enhanced_12-18m_1234",
  "original_topic": "去动物园",
  "target_age": "12-18m",
  "emotion_analysis": {
    "emotion_trajectory": {...},
    "guidance_strategies": {...}
  },
  "infant_scenario": {
    "scenario_background": "...",
    "infant_behaviors": {...},
    "caregiver_guidance": {...}
  },
  "professional_enhancement": {...}
}
```

## 🛠️ 技术实现

### 核心文件
1. **qwen_enhanced_dataset_builder.py** - 主要构建器
2. **qwen_api_config.py** - API配置管理
3. **qwen_usage_example.py** - 使用演示
4. **demo_qwen_analysis.py** - 分析演示

### API调用优化
- **频率控制**: 每次调用间隔2秒，避免超限
- **错误处理**: 自动重试和降级处理
- **成本控制**: 估算和监控API使用成本
- **质量保证**: 多层验证和后备方案

## 💰 成本分析

### 估算参数
- **处理100个样本**: 约¥3.60
- **每个样本成本**: 约¥0.036
- **API调用次数**: 每样本3次（情感分析+场景适配+专业增强）

### 成本优化建议
1. **批量处理**: 减少API调用频率
2. **智能降级**: API失败时使用规则生成
3. **分阶段处理**: 先处理少量样本验证效果
4. **参数调优**: 优化prompt减少token消耗

## 🚀 使用指南

### 快速开始
```bash
# 1. 设置API密钥
python3 qwen_api_config.py

# 2. 查看演示效果
python3 qwen_usage_example.py

# 3. 开始构建数据集
python3 qwen_enhanced_dataset_builder.py
```

### 配置选项
```python
# 在qwen_enhanced_dataset_builder.py中调整
builder.build_dataset(
    input_file="child_chat_data.json",
    output_file="qwen_enhanced_infant_dataset.json",
    target_ages=["0-6m", "6-12m", "12-18m", "18-24m", "24-36m"],
    max_samples=100  # 可调整样本数量
)
```

## 📈 预期成果

### 数据集特点
- **高质量**: 基于Qwen API的深度分析
- **专业性**: 融入多个专业理论框架
- **适龄性**: 严格按婴幼儿发育特点设计
- **多样性**: 覆盖5个年龄段和多种场景类型

### 应用价值
1. **LLM训练**: 训练专业的婴幼儿发育指导模型
2. **临床应用**: 支持早期筛查和干预指导
3. **家长教育**: 提供科学的育儿知识和实践指导
4. **专业培训**: 支持早教工作者和医护人员培训

## 🔧 质量控制

### 多层保障
1. **API质量**: Qwen API的专业分析能力
2. **规则备份**: API失败时的规则生成机制
3. **专家审核**: 建议邀请专家进行内容审核
4. **持续优化**: 根据使用反馈持续改进

### 验证机制
- **格式验证**: 确保JSON结构正确
- **内容检查**: 验证年龄适宜性和专业性
- **一致性检查**: 确保数据内部逻辑一致
- **效果评估**: 在实际应用中验证效果

## 📋 项目文件清单

### 核心代码
- `qwen_enhanced_dataset_builder.py` - 主构建器
- `qwen_api_config.py` - API配置
- `qwen_usage_example.py` - 使用演示
- `demo_qwen_analysis.py` - 分析演示

### 配置文件
- `qwen_config.json` - API配置文件（运行时生成）

### 文档
- `qwen_api_guide.md` - 详细使用指南
- `QWEN_PROJECT_SUMMARY.md` - 项目总结（本文档）

### 输出数据
- `qwen_enhanced_infant_emotion_dataset.json` - 生成的数据集

## 🎯 下一步计划

### 短期目标
1. **API设置**: 获取并配置Qwen API密钥
2. **小规模测试**: 处理10-20个样本验证效果
3. **参数调优**: 根据测试结果优化提示词和参数
4. **质量评估**: 邀请专家评估生成内容质量

### 中期目标
1. **批量生成**: 处理完整的5000条数据
2. **质量控制**: 建立系统的质量检查流程
3. **格式转换**: 生成多种LLM训练格式
4. **效果验证**: 在实际应用中测试数据集效果

### 长期目标
1. **持续优化**: 根据使用反馈持续改进
2. **扩展应用**: 支持更多年龄段和发展领域
3. **多模态**: 考虑加入图像、视频等多模态数据
4. **国际化**: 支持多语言和跨文化适配

## 📞 技术支持

### 常见问题
1. **API调用失败**: 检查密钥和网络连接
2. **生成质量不佳**: 调整提示词和参数
3. **成本控制**: 使用小批量测试和参数优化
4. **格式问题**: 检查JSON结构和编码

### 联系方式
- 技术问题：查看错误日志和文档
- 改进建议：提交Issue或Pull Request
- 学术合作：联系项目维护者

---

*通过结合Qwen API的强大能力和专业的婴幼儿发展知识，我们可以构建出高质量、专业性强的社会情绪发展指导数据集，为AI在早期儿童发展领域的应用奠定坚实基础。*
